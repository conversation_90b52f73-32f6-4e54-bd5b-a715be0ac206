package scanner

import (
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"heartk-go/pkg/types"

	"github.com/saintfish/chardet"
)

// FileScanner 文件扫描器
type FileScanner struct {
	jsEngine *JSEngine
}

// NewFileScanner 创建新的文件扫描器
func NewFileScanner() (*FileScanner, error) {
	jsEngine, err := NewJSEngine()
	if err != nil {
		return nil, fmt.Errorf("failed to create JS engine: %v", err)
	}
	
	return &FileScanner{
		jsEngine: jsEngine,
	}, nil
}

// ScanFile 扫描单个文件
func (fs *FileScanner) ScanFile(filePath string, verbose bool) (*types.ScanResult, error) {
	// 检测文件编码
	_, err := fs.detectEncoding(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to detect encoding for %s: %v", filePath, err)
	}
	
	// 读取文件内容
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %v", filePath, err)
	}
	
	// 转换为字符串，忽略无法解码的字节
	contentStr := string(content)
	
	// 使用JavaScript引擎提取信息
	result, err := fs.jsEngine.ExtractInfo(contentStr)
	if err != nil {
		return nil, fmt.Errorf("failed to extract info from %s: %v", filePath, err)
	}
	
	// 如果启用详细输出，打印统计信息
	if verbose {
		fmt.Println(result.PrintSummary(filePath))
	}
	
	return result, nil
}

// ScanDirectory 扫描目录
func (fs *FileScanner) ScanDirectory(dirPath string, verbose bool) (*types.ScanResult, error) {
	var jsFiles []string
	
	// 递归获取所有JS和HTML文件
	err := fs.getJSFiles(dirPath, &jsFiles)
	if err != nil {
		return nil, fmt.Errorf("failed to get JS files: %v", err)
	}
	
	// 扫描所有文件
	return fs.ScanFiles(jsFiles, verbose)
}

// ScanFiles 扫描文件列表
func (fs *FileScanner) ScanFiles(filePaths []string, verbose bool) (*types.ScanResult, error) {
	allResult := types.NewScanResult()
	
	for _, filePath := range filePaths {
		result, err := fs.ScanFile(filePath, verbose)
		if err != nil {
			fmt.Printf("Warning: %v\n", err)
			continue
		}
		
		allResult.Merge(result)
	}
	
	// 去重和排序
	allResult.Deduplicate()
	
	return allResult, nil
}

// getJSFiles 递归获取目录下的所有JS和HTML文件
func (fs *FileScanner) getJSFiles(dirPath string, jsFiles *[]string) error {
	entries, err := ioutil.ReadDir(dirPath)
	if err != nil {
		return err
	}
	
	for _, entry := range entries {
		fullPath := filepath.Join(dirPath, entry.Name())
		
		if entry.IsDir() {
			// 递归处理子目录
			err := fs.getJSFiles(fullPath, jsFiles)
			if err != nil {
				return err
			}
		} else {
			// 检查文件扩展名
			ext := strings.ToLower(filepath.Ext(entry.Name()))
			if ext == ".js" || ext == ".html" {
				*jsFiles = append(*jsFiles, fullPath)
			}
		}
	}
	
	return nil
}

// detectEncoding 检测文件编码
func (fs *FileScanner) detectEncoding(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	
	// 读取文件的前1024字节用于编码检测
	buffer := make([]byte, 1024)
	n, err := file.Read(buffer)
	if err != nil && n == 0 {
		return "", err
	}
	
	// 使用chardet检测编码
	detector := chardet.NewTextDetector()
	result, err := detector.DetectBest(buffer[:n])
	if err != nil {
		return "utf-8", nil // 默认使用UTF-8
	}
	
	if result.Charset == "" {
		return "utf-8", nil
	}
	
	return result.Charset, nil
}

// Close 关闭文件扫描器
func (fs *FileScanner) Close() {
	if fs.jsEngine != nil {
		fs.jsEngine.Close()
	}
}
