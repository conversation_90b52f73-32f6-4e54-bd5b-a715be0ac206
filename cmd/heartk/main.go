package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"heartk-go/internal/config"
	"heartk-go/internal/reporter"
	"heartk-go/internal/scanner"
	"heartk-go/pkg/types"

	"github.com/spf13/cobra"
)

var (
	verbose    bool
	exportPath string
)

func main() {
	// 打印ASCII艺术字
	fmt.Print(` ___  ___      _______       ________      ________      _________    ___  __       
|\  \|\  \    |\  ___ \     |\   __  \    |\   __  \    |\___   ___\ |\  \|\  \     
\ \  \\\  \   \ \   __/|    \ \  \|\  \   \ \  \|\  \   \|___ \  \_| \ \  \/  /|_   
 \ \   __  \   \ \  \_|/__   \ \   __  \   \ \   _  _\       \ \  \   \ \   ___  \  
  \ \  \ \  \   \ \  \_|\ \   \ \  \ \  \   \ \  \\  \|       \ \  \   \ \  \\ \  \ 
   \ \__\ \__\   \ \_______\   \ \__\ \__\   \ \__\\ _\        \ \__\   \ \__\\ \__\
    \|__|\|__|    \|_______|    \|__|\|__|    \|__|\|__|        \|__|    \|__| \|__|

`)

	var rootCmd = &cobra.Command{
		Use:   "heartk [path]",
		Short: "HeartK - FindSomething本地移植版Go重构",
		Long:  `HeartK是一个敏感信息扫描工具，可以扫描本地文件、目录或网站列表中的敏感信息。`,
		Args:  cobra.ExactArgs(1),
		RunE:  runScan,
	}

	rootCmd.Flags().BoolVarP(&verbose, "verbose", "d", false, "输出详细信息")
	rootCmd.Flags().StringVarP(&exportPath, "export", "e", "", "指定要导出报告的路径")

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func runScan(cmd *cobra.Command, args []string) error {
	scanPath := args[0]
	
	// 加载配置
	cfg := config.LoadConfig()
	
	// 检查扫描路径类型
	info, err := os.Stat(scanPath)
	if err != nil {
		return fmt.Errorf("指定的扫描路径并非有效路径: %v", err)
	}
	
	var result *types.ScanResult
	var hostname string
	
	if info.IsDir() {
		// 扫描目录
		result, err = scanDirectory(scanPath)
	} else {
		// 检查文件类型
		ext := strings.ToLower(filepath.Ext(scanPath))
		if ext == ".js" || ext == ".html" {
			// 扫描单个文件
			result, err = scanFile(scanPath)
		} else {
			// 扫描网站列表
			result, hostname, err = scanWebsiteList(scanPath, cfg)
		}
	}
	
	if err != nil {
		return err
	}
	
	// 生成报告
	reporter := reporter.NewHTMLReporter()
	err = reporter.GenerateReport(result, exportPath, hostname)
	if err != nil {
		return fmt.Errorf("failed to generate report: %v", err)
	}
	
	// 打印总结信息
	if verbose {
		fmt.Printf("\033[31m此次共搜索到：%d个sfz\t%d个mobile\t%d个mail\t%d个ip\t%d个ip_port\t%d个domain\t%d个path\t%d个incomplete_path\t%d个url\t%d个jwt\t%d个algorithm\t%d个secret\t%d个static\033[0m\n",
			len(result.SFZ), len(result.Mobile), len(result.Mail), len(result.IP), len(result.IPPort),
			len(result.Domain), len(result.Path), len(result.IncompletePath), len(result.URL),
			len(result.JWT), len(result.Algorithm), len(result.Secret), len(result.Static))
	}
	
	return nil
}

func scanDirectory(dirPath string) (*types.ScanResult, error) {
	fileScanner, err := scanner.NewFileScanner()
	if err != nil {
		return nil, fmt.Errorf("failed to create file scanner: %v", err)
	}
	defer fileScanner.Close()
	
	return fileScanner.ScanDirectory(dirPath, verbose)
}

func scanFile(filePath string) (*types.ScanResult, error) {
	fileScanner, err := scanner.NewFileScanner()
	if err != nil {
		return nil, fmt.Errorf("failed to create file scanner: %v", err)
	}
	defer fileScanner.Close()
	
	return fileScanner.ScanFile(filePath, verbose)
}

func scanWebsiteList(listPath string, cfg *types.ScanConfig) (*types.ScanResult, string, error) {
	// 读取网站列表
	websites, err := readWebsiteList(listPath)
	if err != nil {
		return nil, "", fmt.Errorf("failed to read website list: %v", err)
	}
	
	if len(websites) == 0 {
		return nil, "", fmt.Errorf("%s未读取到有效数据", listPath)
	}
	
	// 创建网站扫描器
	webScanner, err := scanner.NewWebScanner(cfg)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create web scanner: %v", err)
	}
	defer webScanner.Close()
	
	// 创建报告生成器
	htmlReporter := reporter.NewHTMLReporter()
	
	// 逐个扫描网站
	for _, website := range websites {
		result, err := webScanner.ScanWebsite(website, verbose)
		if err != nil {
			fmt.Printf("Warning: failed to scan %s: %v\n", website, err)
			continue
		}
		
		// 为每个网站生成单独的报告
		hostname := webScanner.GetHostname(website)
		err = htmlReporter.GenerateReport(result, exportPath, hostname)
		if err != nil {
			fmt.Printf("Warning: failed to generate report for %s: %v\n", website, err)
		}
	}
	
	// 返回空结果，因为已经为每个网站生成了单独的报告
	return types.NewScanResult(), "", nil
}

func readWebsiteList(filePath string) ([]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	
	var websites []string
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			websites = append(websites, line)
		}
	}
	
	if err := scanner.Err(); err != nil {
		return nil, err
	}
	
	return websites, nil
}
