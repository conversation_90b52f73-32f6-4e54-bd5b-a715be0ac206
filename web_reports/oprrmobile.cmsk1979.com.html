<!--  
@Date    : 2020-09-12 16:26:48
<AUTHOR> residuallaugh 
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
</head>
<body style="width:780px; font-size: 14px;">
    <div style="width:780px; height: 30px; margin-left: 15px;">
        <a href="report.html"><div id="Zhuye" style="width: 55px; height: 28px; float: left; text-align: center; line-height: 28px; font-size: 14px; background: #000000; color: #ffffff; border: 1px solid black;border-radius: 2px 0px 0px 2px;">主页</div></a>
    </div>
    <div style="width:780px; height: 800px; margin-left: 15px;">
        <div id="taskstatus" style="height: 34px; line-height: 34px;"></div>
        <div style="width: 256px; float: left; border-right: 1px solid #e8e8e8;">
            <div class="findsomething_title" id="popupIp">IP</div><button type="button" class="copy" name="ip">复制</button>
            <p id="ip" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupIpPort">IP_PORT</div><button class="copy" name="ip_port">复制</button>
            <p id="ip_port" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupDomain">域名</div><button class="copy" name="domain">复制</button>
            <p id="domain" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupSfz">身份证</div><button class="copy" name="sfz">复制</button>
            <p id="sfz" style="">🈚️</p>
            <div class="findsomething_title" id="popupMobile">手机号</div><button class="copy" name="mobile">复制</button>
            <p id="mobile" style="">🈚️</p>
            <div class="findsomething_title" id="popupMail">邮箱</div><button class="copy" name="mail">复制</button>
            <p id="mail" style="">🈚️</p>
            <div class="findsomething_title" id="popupJwt">JWT<button class="copy" name="jwt">复制</button></div>
            <p id="jwt" style="word-break:break-word;">🈚️</p>
            <div class="findsomething_title" id="popupAlgorithm">算法</div><button class="copy" name="algorithm">复制</button>
            <p id="algorithm" style="">🈚️</p>
            <div class="findsomething_title" id="popupSecret">Secret</div><button class="copy" name="secret">复制</button>
            <p id="secret" style="">🈚️</p>
        </div>
        <div style="width: 480px; height: 800px; float: left; margin-left:16px;">
            <div class="findsomething_title" id="popupPath">Path</div><button id="path_button" class="copy" name="path">复制</button>
            <p id="path" style="">🈚️</p>
            <div class="findsomething_title" id="popupIncompletePath">IncompletePath</div><button class="copy" name="incomplete_path">复制</button>
            <p id="incomplete_path" style="">🈚️</p>
            <div class="findsomething_title" id="popupUrl">Url</div><button class="copy" name="url">复制</button>
            <p id="url" style="">🈚️</p>
            <div class="findsomething_title" id="popupStaticPath">StaticUrl</div><button class="copy" name="static">复制</button>
            <p id="static" style="">🈚️</p>
        </div>
</div>
</body>
<script>
    
    // @Date    : 2020-09-12 16:26:48
    // <AUTHOR> residuallaugh

    var key = ["ip","ip_port","domain","path","incomplete_path","url","static","sfz","mobile","mail","jwt","algorithm","secret"]

    let messages = {
        "popupCopy": "复制",
        "popupCopyurl": "复制URL",
        "popupTipClickBeforeCopy": "请点击原页面后再复制：）"
    };

    function getMessage(key) {
        return messages[key] || "";
    }

    function init_copy() {
        var elements = document.getElementsByClassName("copy");
        if(elements){
            for (var i=0, len=elements.length|0; i<len; i=i+1|0) {
                elements[i].textContent = getMessage("popupCopy");
                let ele_name = elements[i].name;
                let ele_id = elements[i].id;
                if (ele_id == "popupCopyurl"){
                    elements[i].textContent = getMessage("popupCopyurl");
                }
                elements[i].onclick=function () {
                    var inp =document.createElement('textarea');
                    document.body.appendChild(inp)
                    var copytext = document.getElementById(ele_name).textContent;
                    inp.value = copytext;
                    inp.select();
                    document.execCommand('copy',false);
                    inp.remove();
                }
            }
        }
    };

    function show_info(result_data) {
        for (var k in key){
            if (result_data[key[k]]){
                let container = document.getElementById(key[k]);
                while((ele = container.firstChild)){
                    ele.remove();
                }
                container.style.whiteSpace = "pre";
                for (var i in result_data[key[k]]){
                    let tips = document.createElement("div");
                    tips.setAttribute("class", "tips")
                    let link = document.createElement("a");
                    link.appendChild(tips);
                    let span = document.createElement("span");
                    span.textContent = result_data[key[k]][i]+'\n';
                    container.appendChild(link);
                    container.appendChild(span);
                }
            }
        }
    }

    init_copy();

    show_info({"sfz":[],"mobile":[],"mail":[],"ip":[],"ip_port":[],"domain":["invalid.tel","https://img01.yzcdn.cn","http://www.w3.org","fields.lamp.red"],"path":["/script","/a/i","/./","/a/b","/#","/.","/api/process/v1/instances/","/info","/api/process/v1/processes/ids","/api/platform/v1/agent-users","/api/process/endpoints/business-data/start-user","/api/process/v1/process-auth/topic-key-parameter","/cancel","./af","./ar","./ar-dz","./ar-kw","./ar-ly","./ar-ma","./ar-sa","./ar-tn","./az","./be","./bg","./bm","./bn","./bn-bd","./bo","./br","./bs","./ca","./cs","./cv","./cy","./da","./de","./de-at","./de-ch","./dv","./el","./en-au","./en-ca","./en-gb","./en-ie","./en-il","./en-in","./en-nz","./en-sg","./eo","./es","./es-do","./es-mx","./es-us","./et","./eu","./fa","./fi","./fil","./fo","./fr","./fr-ca","./fr-ch","./fy","./ga","./gd","./gl","./gom-deva","./gom-latn","./gu","./he","./hi","./hr","./hu","./hy-am","./id","./is","./it","./it-ch","./ja","./jv","./ka","./kk","./km","./kn","./ko","./ku","./ky","./lb","./lo","./lt","./lv","./me","./mi","./mk","./ml","./mn","./mr","./ms","./ms-my","./mt","./my","./nb","./ne","./nl","./nl-be","./nn","./oc-lnc","./pa-in","./pl","./pt","./pt-br","./ro","./ru","./sd","./se","./si","./sk","./sl","./sq","./sr","./sr-cyrl","./ss","./sv","./sw","./ta","./te","./tet","./tg","./th","./tk","./tl-ph","./tlh","./tr","./tzl","./tzm","./tzm-latn","./ug-cn","./uk","./ur","./uz","./uz-latn","./vi","./x-pseudo","./yo","./zh-cn","./zh-hk","./zh-mo","./zh-tw","/start?bsid=","/login","/auth/i18Languages","/auth/captcha","/todo","/map","/api/platform/v1/manage/search-organizations","/api/platform/v1/documents/form-files-upload","/api/api-centre","/api/platform/v1/manage/tree-organizations","/api/platform/v1/organizations/","/select-users-status?invalidState=","/api/platform/v1/select-users-status?keyword=","/api/process/v1/common-approval-comment","/auth/state","/auth/impersonate","/user-state","/auth/config","/auth/check-token?moduleCode=","/auth/mobile-impersonate?token=","/auth/authority-view","/api/bpa/v1/authorities/mobile-business-types/","/api/bpa/v1/manage/domains/list?isall=1","/api/bpa/v1/manage/businessTypes/1/tree-business-types?domain=","/api/bpa/v1/manage/businessTypes/MobileTitle/","/api/bpa/v1/mobile/access-log/","/process-authority?business-id=","/home","/process-authority","/api/","/api/*","/welcome","/approve","/di-view","/logout","/401","/api/form/v1/templates/extension-data","/api/platform/v1/select-dictionaries","/api/process/v1/manage/process-paramter-modules/getSystemParByProcessTemplateID","/api/engine/v1/instances/","/steps","/api/engine/v1/process-steps","/api/engine/v1/process-main-params","/api/process/v1/process-default-setting/step","/api/todo-centre/v1/tasks/get-readrecord","/api/process/v1/documents/upload","/attachments","/api/process/v1/documents/download/","/api/process/v1/documents/preview/","/relations","/api/process/v1/relative-instances","/control-points","/api/process/v1/control-point/key-parameter","/api/form/v1/templates/mobile","/form-params","/api/process/endpoints/business-data/forms","/api/process/v1/field-right"],"incomplete_path":["DD/MM/YYYY","YYYY/MM/DD","MM/D/YYYY","Chrome/66","DD/M/YYYY","D/M/YYYY","YYYY/M/D","MM/DD/YYYY","text/plain","text/html","image/","edge/","text/javascript","./","./af","./ar","./az","./be","./bg","./bm","./bn","./bo","./br","./bs","./ca","./cs","./cv","./cy","./da","./de","./dv","./el","./eo","./es","./et","./eu","./fa","./fi","./fil","./fo","./fr","./fy","./ga","./gd","./gl","./gu","./he","./hi","./hr","./hu","./id","./is","./it","./ja","./jv","./ka","./kk","./km","./kn","./ko","./ku","./ky","./lb","./lo","./lt","./lv","./me","./mi","./mk","./ml","./mn","./mr","./ms","./mt","./my","./nb","./ne","./nl","./nn","./pl","./pt","./ro","./ru","./sd","./se","./si","./sk","./sl","./sq","./sr","./ss","./sv","./sw","./ta","./te","./tet","./tg","./th","./tk","./tlh","./tr","./tzl","./tzm","./uk","./ur","./uz","./vi","./yo","doc/download","image/png"],"url":["http://www.w3.org/1999/xlink","http://www.w3.org/2000/svg","http://www.w3.org/1998/Math/MathML","https://img01.yzcdn.cn/vant/empty-image-","https://img01.yzcdn.cn/vant/share-sheet-","invalid.tel","fields.lamp.red"],"jwt":[],"algorithm":[".btoa(",".atob(","(btoa(","=btoa(","+btoa("],"secret":["DOMTokenList:1","tokensToFunction=j","tokensToRegExp=H","token:e","tokens:s","tokens:r","CancelToken=n","password=\"Matrix@2021\"","account:e","account=e","account=t","token:a","UisToken:t","userName=n","userName:JSON","userName=e","userName:_MobileStandard_services_auth__WEBPACK_IMPORTED_MODULE_14__","userName=t","userName=_MobileStandard_services_auth__WEBPACK_IMPORTED_MODULE_14__","userName:t","userName:\"todo\"","userName:e","userName=null","uploadUserName:a","password:t","password=\"Matrix","password=t","UserName:a"],"static":["https://img01.yzcdn.cn/vant/coupon-empty.png","https://img01.yzcdn.cn/upload_files/2020/06/24/FmKWDg0bN9rMcTp9ne8MXiQWGtLn.png","./af.js","./ar-dz.js","./ar-kw.js","./ar-ly.js","./ar-ma.js","./ar-sa.js","./ar-tn.js","./ar.js","./az.js","./be.js","./bg.js","./bm.js","./bn-bd.js","./bn.js","./bo.js","./br.js","./bs.js","./ca.js","./cs.js","./cv.js","./cy.js","./da.js","./de-at.js","./de-ch.js","./de.js","./dv.js","./el.js","./en-au.js","./en-ca.js","./en-gb.js","./en-ie.js","./en-il.js","./en-in.js","./en-nz.js","./en-sg.js","./eo.js","./es-do.js","./es-mx.js","./es-us.js","./es.js","./et.js","./eu.js","./fa.js","./fi.js","./fil.js","./fo.js","./fr-ca.js","./fr-ch.js","./fr.js","./fy.js","./ga.js","./gd.js","./gl.js","./gom-deva.js","./gom-latn.js","./gu.js","./he.js","./hi.js","./hr.js","./hu.js","./hy-am.js","./id.js","./is.js","./it-ch.js","./it.js","./ja.js","./jv.js","./ka.js","./kk.js","./km.js","./kn.js","./ko.js","./ku.js","./ky.js","./lb.js","./lo.js","./lt.js","./lv.js","./me.js","./mi.js","./mk.js","./ml.js","./mn.js","./mr.js","./ms-my.js","./ms.js","./mt.js","./my.js","./nb.js","./ne.js","./nl-be.js","./nl.js","./nn.js","./oc-lnc.js","./pa-in.js","./pl.js","./pt-br.js","./pt.js","./ro.js","./ru.js","./sd.js","./se.js","./si.js","./sk.js","./sl.js","./sq.js","./sr-cyrl.js","./sr.js","./ss.js","./sv.js","./sw.js","./ta.js","./te.js","./tet.js","./tg.js","./th.js","./tk.js","./tl-ph.js","./tlh.js","./tr.js","./tzl.js","./tzm-latn.js","./tzm.js","./ug-cn.js","./uk.js","./ur.js","./uz-latn.js","./uz.js","./vi.js","./x-pseudo.js","./yo.js","./zh-cn.js","./zh-hk.js","./zh-mo.js","./zh-tw.js","./Avatar_default.png","./bmp.png","./content_bg.png","./content_box.png","./default.png","./excel.png","./icon_tabright.png","./jpg.png","./logo_movitech.png","./logo_project.png","./pdf.png","./png.png","./ppt.png","./right_bg.png","./status_pin_approved.png","./status_pin_refused.png","./txt.png","./world.png","./yasuobao.png"]})
</script>
<style type="text/css">
    .copy {
        border-style: none;
        background-color: #ffffff;
        float: right;
        margin-right: 16px;
        
    }
    .findsomething_title {
        font-size: 16px;
        font-weight: bold;
        border-left: 4px solid black;
        text-indent: 4px;
        height: 16px;
        line-height: 16px;
    }
    .tips {
        display: inline-block;
        border-top: 0.2px solid;
        border-right: 0.2px solid;
        width: 10px;
        height: 10px;
        border-color: #EA6000;
        transform: rotate(-135deg);
    }
    a{
        text-decoration:none;
        color:#333;
    }
    button{
        cursor: pointer
    }
</style>
</html>