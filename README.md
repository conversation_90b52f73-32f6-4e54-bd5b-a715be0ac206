# HeartK-Go

HeartK的Go语言重构版本，基于原Python项目进行完全重构，使用Go+JavaScript混合架构。

## 项目介绍

本项目是HeartK的Go语言重构版本，保持了与原版完全一致的功能和扫描规则。HeartK是基于残笑大佬的[FindSomething](https://github.com/momosecurity/FindSomething)插件进行的本地移植版，可对指定的目录下的所有html、js文件或单个html、js文件进行扫描并获取敏感信息，也可对指定的站点列表进行批量扫描。

## 技术架构

- **Go语言**：负责文件处理、HTTP请求、命令行界面、报告生成
- **JavaScript引擎**：使用goja执行原版JavaScript正则表达式逻辑
- **完全兼容**：与原Python版本功能和输出格式完全一致

## 优势

1. **性能更好**：Go语言的高性能和并发特性
2. **部署简单**：单一可执行文件，无需Python环境
3. **跨平台**：支持Windows、Linux、macOS
4. **内存效率**：更低的内存占用
5. **规则一致**：复用原版JavaScript正则表达式，确保扫描结果一致

## 安装

### 从源码编译

```bash
cd heartk-go
go mod tidy
go build -o heartk cmd/heartk/main.go
```

### 直接下载

从Releases页面下载对应平台的预编译二进制文件。

## 使用方法

### 基本用法

扫描目录、文件或网站列表：
```bash
./heartk [扫描路径]
```

### 选项参数

- `-d, --verbose`: 输出详细信息
- `-e, --export [路径]`: 指定报告导出路径

### 使用示例

1. **扫描目录**：
```bash
./heartk /path/to/scan
```

2. **扫描单个文件**：
```bash
./heartk /path/to/file.js
```

3. **批量扫描网站**：
```bash
./heartk websites.txt
```

4. **详细输出**：
```bash
./heartk /path/to/scan -d
```

5. **指定导出路径**：
```bash
./heartk /path/to/scan -e /output/path
```

## 网站列表格式

进行批量扫描前将要扫描的站点导入到一个文本文件中，该文本文件需具有以下格式：

1. 每个站点需独占一行
2. 每个站点必须有协议头，也就是形如`http://xxx.xxx.com`的格式

示例：
```
https://example.com
http://test.example.com
https://api.example.com:8080
```

## 输出说明

1. 工具扫描完毕后会将敏感信息导出在指定的目录或运行工具的目录下的`report.html`中
2. 如果没有指定导出报告的路径，批量扫描网站后的报告会自动保存到工具目录下的`web_reports`文件夹下
3. 每个网站会生成单独的HTML报告文件

## 扫描类型

工具可以检测以下类型的敏感信息：

- **身份证号** (sfz)
- **手机号** (mobile)  
- **邮箱地址** (mail)
- **IP地址** (ip)
- **IP:端口** (ip_port)
- **域名** (domain)
- **路径** (path)
- **不完整路径** (incomplete_path)
- **URL** (url)
- **JWT令牌** (jwt)
- **算法** (algorithm)
- **密钥** (secret)
- **静态文件** (static)

## 项目结构

```
heartk-go/
├── cmd/heartk/          # 主程序入口
├── internal/
│   ├── config/          # 配置管理
│   ├── scanner/         # 扫描器模块
│   └── reporter/        # 报告生成器
├── pkg/types/           # 数据类型定义
├── assets/              # JavaScript资源文件
└── web/                 # Web模板文件
```

## 与原版差异

1. **运行环境**：无需Python和Node.js环境，单一Go可执行文件
2. **性能**：更快的扫描速度和更低的内存占用
3. **部署**：更简单的部署和分发
4. **功能**：与原版功能完全一致
5. **输出**：报告格式与原版完全兼容

## 开发

### 依赖

- Go 1.21+
- github.com/dop251/goja (JavaScript引擎)
- github.com/PuerkitoBio/goquery (HTML解析)
- github.com/spf13/cobra (命令行框架)
- github.com/saintfish/chardet (字符编码检测)

### 构建

```bash
go mod tidy
go build -o heartk cmd/heartk/main.go
```

### 测试

```bash
go test ./...
```

## 注意事项

1. **如要指定导出报告的路径，只写目录即可，不要指定文件名**
2. **网站请求失败或js请求失败可能是由于触发了风控机制导致的**
3. **工具会自动处理字符编码问题，忽略无法解码的字节**

## 鸣谢

- 本项目基于残笑大佬的[FindSomething](https://github.com/momosecurity/FindSomething)插件
- 原Python版本HeartK项目

## License

本项目遵循与原项目相同的开源协议。
